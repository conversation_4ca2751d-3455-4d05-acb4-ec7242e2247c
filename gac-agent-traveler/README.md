# GAC Agent Traveler - AI旅行家智能助手

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115+-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 🚗 专为广汽集团车载场景设计的智能旅行助手，基于大语言模型提供个性化旅行规划和景点推荐服务。

## ✨ 核心特性

- 🧠 **智能意图识别**: 自动理解用户旅行需求
- 🔍 **多引擎搜索**: 集成腾讯、Bing等多个搜索引擎
- 📍 **POI信息增强**: 结合高德地图提供详细景点信息
- 💬 **流式响应**: 实时返回旅行建议，提升用户体验
- 🚀 **高性能**: 支持并发处理，响应时间<5秒
- 🛡️ **安全可靠**: 内置敏感词过滤和安全检测

## 🚀 快速开始

### 环境要求

- Python 3.11+
- Redis (可选，用于缓存)

### 安装步骤

1. **克隆项目**
```bash
git clone https://gitlab.senseauto.com/drive/Auto-Cloud/services/gac-agent-traveler.git
cd gac-agent-traveler
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 编辑环境变量文件
vim .env

# 必需的环境变量
SENSEAUTO_KEY=your_senseauto_api_key
SENSEAUTO_URL=your_senseauto_url
TENCENT_AK=your_tencent_access_key
TENCENT_SK=your_tencent_secret_key
```

4. **启动服务**
```bash
# 开发模式
python main.py

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8080 --workers 2
```

5. **验证服务**
```bash
# 健康检查
curl http://localhost:8080/gac-agent-traveler/v1/health

# 测试API
python test_script/maoyan/AI_Traveler_Test_For_Dify_Fixed_01.py
```

## 📡 API使用示例

### 基础查询

```bash
curl -X POST "http://localhost:8080/gac-agent-traveler/v1/ai_traveler" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "北京有什么好玩的地方？",
    "stream": true,
    "k": 5,
    "engine": "tencent",
    "location": {
      "lat": "39.9042",
      "lon": "116.4074"
    },
    "user_info": {
      "car_id": "test_car_id",
      "user_id": "test_user_id",
      "category": ["旅游", "景点"]
    }
  }'
```

### 流式响应示例

```json
data: {"type": "pre_intent", "data": "相关", "messageId": "xxx"}
data: {"type": "t3-intent", "data": ["北京 旅游景点"], "messageId": "xxx"}
data: {"type": "message", "data": "北京有很多著名景点，推荐故宫、天安门...", "messageId": "xxx"}
data: {"type": "messageEnd", "time_cost": {...}, "messageId": "xxx"}
```

## 🏗️ 系统架构

```
用户请求 → API网关 → WebSearchAgent → 多引擎搜索 → 内容爬取 → LLM总结 → POI增强 → 流式响应
```

### 核心组件

- **WebSearchAgent**: 核心处理引擎，负责整个对话流程
- **多引擎搜索**: 并发调用腾讯、Bing等搜索引擎
- **内容爬取**: 支持Go-readability和Python两种爬取方式
- **LLM服务**: 集成SenseAuto等大语言模型
- **POI服务**: 结合高德地图API提供详细景点信息

## 🧪 测试

项目提供了多个测试脚本：

```bash
# 基础功能测试
python test_script/maoyan/AI_Traveler_Test_For_Dify_Fixed_01.py

# 流式响应测试
python test_script/maoyan/AI_Traveler_Test_Stream_02.py

# 禁用缓存测试
python test_script/maoyan/AI_Traveler_Test_NoCache_03.py
```

## 🔧 配置说明

### 核心配置项

| 配置项        | 说明              | 必需 |
| ------------- | ----------------- | ---- |
| SENSEAUTO_KEY | SenseAuto API密钥 | ✅    |
| TENCENT_AK/SK | 腾讯云访问密钥    | ✅    |
| REDIS_HOST    | Redis服务地址     | ❌    |
| GAODE_API_KEY | 高德地图API密钥   | ❌    |

详细配置请参考 [完整文档](README_PROJECT.md)

## 🚨 故障排除

### 常见问题

1. **环境变量未定义错误**
   ```bash
   # 解决方案：检查.env文件配置
   source .env
   ```

2. **Redis连接失败**
   ```bash
   # 解决方案：禁用缓存或配置Redis
   use_search_cache: false
   ```

3. **搜索引擎无结果**
   ```bash
   # 解决方案：检查API密钥和网络连接
   ```

## 📊 性能指标

- **TTFB (首字节时间)**: < 1秒
- **意图识别**: < 0.5秒
- **搜索响应**: < 2秒
- **总体响应**: < 10秒

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者: GAC AI Team
- 技术支持: [提交Issue](https://gitlab.senseauto.com/drive/Auto-Cloud/services/gac-agent-traveler/-/issues)
- 详细文档: [README_PROJECT.md](README_PROJECT.md)

---

> 💡 **提示**: 查看 [README_PROJECT.md](README_PROJECT.md) 获取更详细的技术文档和架构说明。


