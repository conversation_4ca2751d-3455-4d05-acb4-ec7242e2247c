# GAC Agent Traveler - AI旅行家智能助手

## 📖 项目简介

GAC Agent Traveler 是一个基于大语言模型的智能旅行助手，专为广汽集团车载场景设计。该系统通过多模态输入、实时搜索、POI信息获取等功能，为用户提供个性化的旅行规划和景点推荐服务。

## 🏗️ 系统架构

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI       │    │  WebSearchAgent │    │   LLM Services  │
│   Web Server    │◄──►│   Core Engine   │◄──►│   (SenseAuto)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Router    │    │  Search Engines │    │  Content Scraper│
│   (Dify兼容)    │    │ (Tencent/Bing)  │    │  (Go/Python)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Redis Cache   │    │   POI Services  │    │  Memory System  │
│   (搜索缓存)     │    │   (高德地图)     │    │  (用户画像)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈

- **Web框架**: FastAPI + Uvicorn
- **异步处理**: asyncio + aiohttp
- **LLM集成**: OpenAI SDK (兼容SenseAuto)
- **搜索引擎**: 腾讯搜索、Bing、Serper
- **缓存系统**: Redis
- **配置管理**: EnvYAML
- **日志监控**: Loguru + Logfire
- **容器化**: Docker

## 🔄 核心处理流程

### 1. 请求处理流程

```mermaid
graph TD
    A[用户请求] --> B[API路由]
    B --> C[获取位置信息]
    B --> D[获取用户画像]
    C --> E[WebSearchAgent]
    D --> E
    E --> F[前置意图识别]
    F --> G[意图重写]
    G --> H[多引擎搜索]
    H --> I[内容爬取]
    I --> J[结果重排序]
    J --> K[LLM总结]
    K --> L[安全检测]
    L --> M[POI信息获取]
    M --> N[流式响应]
```

### 2. WebSearchAgent 核心阶段

#### Stage 0: 意图识别与重写
- **前置意图判断**: 判断查询是否与旅行相关
- **意图重写**: 将用户查询重写为搜索关键词
- **工具选择**: 决定使用web_search还是null工具

#### Stage 1: 多引擎搜索
- **并发搜索**: 同时调用多个搜索引擎
- **结果聚合**: 合并不同引擎的搜索结果
- **缓存机制**: Redis缓存搜索结果

#### Stage 2: 内容爬取与处理
- **网页抓取**: 使用Go-readability或Python爬虫
- **内容清洗**: 提取有效文本内容
- **结果重排**: 使用reranker模型优化结果排序

#### Stage 3: 智能总结
- **LLM总结**: 基于搜索结果生成旅行建议
- **流式输出**: 实时返回生成内容
- **安全检测**: 敏感词过滤

#### Stage 4: POI信息增强
- **景点列表提取**: 从总结中提取POI信息
- **高德地图查询**: 获取详细的POI信息
- **信息补充**: 添加地址、评分等详细信息

## 📁 项目结构

```
gac-agent-traveler/
├── main.py                 # 应用入口
├── configuration.py        # 配置管理
├── env.yaml               # 环境配置
├── requirements.txt       # 依赖包
├── Dockerfile            # 容器配置
├── api/
│   └── router.py         # API路由定义
├── schemas/
│   ├── search.py         # 请求模型
│   ├── response.py       # 响应模型
│   └── models.py         # 数据模型
├── src/
│   ├── utils/
│   │   ├── actions/      # 工具动作
│   │   ├── llms/         # LLM客户端
│   │   ├── memory/       # 记忆系统
│   │   └── web_content_scrape.py  # 网页爬取
│   └── prompts/          # 提示词模板
├── service/
│   └── logger_manager.py # 日志管理
├── test_script/          # 测试脚本
└── travel_yield_demo.py  # 核心处理引擎
```

## 🚀 快速开始

### 环境要求

- Python 3.11+
- Redis (可选，用于缓存)
- Docker (可选，用于容器化部署)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd gac-agent-traveler
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

4. **启动服务**
```bash
# 开发模式
python main.py

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8080 --workers 2
```

### Docker 部署

```bash
# 构建镜像
docker build -t gac-agent-traveler .

# 运行容器
docker run -p 8080:8080 -e REDIS_HOST=your-redis-host gac-agent-traveler
```

## 🔧 配置说明

### 核心配置项

```yaml
# LLM服务配置
llm_server_set:
  senseauto:
    api_key: ${SENSEAUTO_KEY}
    base_url: ${SENSEAUTO_URL}
    model_name: SenseAuto-Chat

# 搜索引擎配置
search_engine:
  tencent:
    ak: ${TENCENT_AK}
    sk: ${TENCENT_SK}
  bing:
    key: ${BING_KEY}

# Redis缓存配置
redis:
  host: ${REDIS_HOST}
  port: ${REDIS_PORT}
  password: ${REDIS_PASSWORD}

# POI服务配置
gaode:
  api_key: ${GAODE_API_KEY}
```

### 环境变量

| 变量名        | 说明              | 必需 |
| ------------- | ----------------- | ---- |
| SENSEAUTO_KEY | SenseAuto API密钥 | ✅    |
| SENSEAUTO_URL | SenseAuto服务地址 | ✅    |
| TENCENT_AK    | 腾讯云访问密钥    | ✅    |
| TENCENT_SK    | 腾讯云私有密钥    | ✅    |
| REDIS_HOST    | Redis服务地址     | ❌    |
| GAODE_API_KEY | 高德地图API密钥   | ❌    |

## 📡 API接口

### 健康检查

```http
GET /gac-agent-traveler/v1/health
```

### AI旅行家对话

```http
POST /gac-agent-traveler/v1/ai_traveler
Content-Type: application/json

{
  "query": "北京有什么好玩的地方？",
  "stream": true,
  "k": 5,
  "engine": "tencent",
  "location": {
    "lat": "39.9042",
    "lon": "116.4074"
  },
  "user_info": {
    "car_id": "test_car_id",
    "user_id": "test_user_id",
    "category": ["旅游", "景点"]
  }
}
```

### 响应格式

流式响应示例：
```json
data: {"type": "pre_intent", "data": "相关", "messageId": "xxx"}
data: {"type": "t3-intent", "data": ["北京 旅游景点"], "messageId": "xxx"}
data: {"type": "message", "data": "北京有很多著名景点...", "messageId": "xxx"}
data: {"type": "messageEnd", "time_cost": {...}, "messageId": "xxx"}
```

## 🧪 测试

### 运行测试脚本

```bash
# 基础功能测试
python test_script/maoyan/AI_Traveler_Test_For_Dify_Fixed_01.py

# 流式响应测试
python test_script/maoyan/AI_Traveler_Test_Stream_02.py

# 禁用缓存测试
python test_script/maoyan/AI_Traveler_Test_NoCache_03.py
```

### 性能指标

- **TTFB (首字节时间)**: < 1秒
- **意图识别**: < 0.5秒
- **搜索响应**: < 2秒
- **总体响应**: < 10秒

## 🔍 监控与日志

### 日志级别

- **INFO**: 正常业务流程
- **ERROR**: 错误信息和异常
- **DEBUG**: 详细调试信息

### 关键指标

- 请求处理时间
- 搜索引擎响应时间
- LLM调用延迟
- 缓存命中率

## 🚨 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务状态
   - 验证连接配置
   - 可禁用缓存继续运行

2. **搜索引擎无结果**
   - 检查API密钥配置
   - 验证网络连接
   - 查看搜索引擎状态

3. **LLM调用超时**
   - 检查模型服务状态
   - 调整超时配置
   - 验证API密钥

### 调试模式

```bash
# 启用调试模式
export DEBUG_MODE=true
python main.py
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔧 高级配置

### 搜索引擎配置

系统支持多种搜索引擎，可根据需要启用：

```yaml
search_engine:
  # 腾讯搜索 (推荐)
  tencent:
    ak: ${TENCENT_AK}
    sk: ${TENCENT_SK}

  # Bing搜索
  bing:
    url: ${BING_URL}
    key: ${BING_KEY}

  # Serper (Google/Bing代理)
  serper:
    key: ${SERPER_KEY}
    google_url: "https://google.serper.dev/search"
    bing_url: "https://bing.serper.dev/search"
```

### LLM模型配置

支持多种大语言模型：

```yaml
llm_server_set:
  # SenseAuto (默认)
  senseauto:
    api_key: ${SENSEAUTO_KEY}
    base_url: ${SENSEAUTO_URL}
    model_name: "SenseAuto-Chat"

  # 字节跳动
  bytedance:
    api_key: ${BYTEDANCE_KEY}
    model_name: "bot-20241015104116-97zdt"
```

### 重排序配置

```yaml
rerank:
  base_url: ${RERANK_URL}
  model_name: "reranker-v1"
  top_k: 5
```

## 🎯 核心算法详解

### 1. 意图识别算法

```python
# 前置意图判断
def pre_intent_detection(query: str) -> str:
    """
    判断用户查询是否与旅行相关
    返回: "相关" | "不相关" | "需要更多信息"
    """

# 意图重写
def intent_rewrite(query: str, location: dict) -> List[str]:
    """
    将自然语言查询重写为搜索关键词
    输入: "北京有什么好玩的地方？"
    输出: ["北京 旅游景点", "北京 必去景点"]
    """
```

### 2. 多引擎搜索策略

```python
async def multi_engine_search(queries: List[str]) -> Dict:
    """
    并发调用多个搜索引擎
    - 腾讯搜索: 中文内容优势
    - Bing搜索: 国际化内容
    - Serper: Google代理服务
    """
    tasks = []
    for query in queries:
        for engine in search_engines:
            tasks.append(engine.search(query, num_results))

    results = await asyncio.gather(*tasks)
    return aggregate_results(results)
```

### 3. 内容爬取与清洗

```python
class WebScrape:
    """
    支持两种爬取方式:
    1. Go-readability: 高性能，适合生产环境
    2. Python-readability: 灵活性高，适合开发调试
    """

    async def scrape_content(self, urls: List[str]) -> List[str]:
        # 并发爬取多个URL
        # 内容去重和清洗
        # 提取核心文本信息
```

### 4. POI信息增强

```python
def extract_poi_from_summary(summary: str) -> List[Dict]:
    """
    从LLM总结中提取POI信息
    使用正则表达式和NLP技术识别景点名称
    """

async def enrich_poi_info(poi_list: List[str]) -> List[Dict]:
    """
    调用高德地图API获取详细POI信息
    - 地址、电话、评分
    - 营业时间、门票价格
    - 交通路线建议
    """
```

## 🔄 数据流图

```mermaid
sequenceDiagram
    participant U as 用户
    participant A as API网关
    participant W as WebSearchAgent
    participant S as 搜索引擎
    participant L as LLM服务
    participant P as POI服务
    participant R as Redis缓存

    U->>A: 发送旅行查询
    A->>W: 创建搜索代理
    W->>L: 前置意图识别
    L-->>W: 返回意图结果
    W->>L: 查询重写
    L-->>W: 返回搜索关键词

    par 并发搜索
        W->>S: 腾讯搜索
        W->>S: Bing搜索
        W->>S: Serper搜索
    end

    S-->>W: 返回搜索结果
    W->>R: 缓存搜索结果
    W->>W: 内容爬取与重排
    W->>L: 生成旅行总结
    L-->>W: 流式返回内容
    W->>P: 获取POI详细信息
    P-->>W: 返回POI数据
    W-->>A: 流式响应
    A-->>U: 返回旅行建议
```

## 📊 性能优化

### 1. 缓存策略

- **搜索结果缓存**: 3小时有效期
- **POI信息缓存**: 24小时有效期
- **LLM响应缓存**: 1小时有效期

### 2. 并发优化

- **搜索引擎并发**: 最多3个引擎同时搜索
- **内容爬取并发**: 最多10个URL同时爬取
- **连接池管理**: aiohttp连接复用

### 3. 资源限制

```yaml
limits:
  max_search_results: 10
  max_crawl_urls: 20
  max_content_length: 2000
  request_timeout: 30s
  llm_timeout: 60s
```

## 🛡️ 安全机制

### 1. 敏感词过滤

```python
# 输入检测
async def input_moderation(text: str) -> bool:
    """检测用户输入是否包含敏感内容"""

# 输出检测
async def output_moderation(text: str) -> str:
    """过滤LLM输出中的敏感内容"""
```

### 2. 访问控制

- API密钥验证
- 请求频率限制
- IP白名单机制

### 3. 数据安全

- 敏感信息脱敏
- 日志数据加密
- 用户隐私保护

## 📈 监控指标

### 业务指标

- 请求成功率: > 99%
- 平均响应时间: < 5秒
- 用户满意度: > 4.5/5

### 技术指标

- CPU使用率: < 70%
- 内存使用率: < 80%
- 缓存命中率: > 60%

### 告警规则

```yaml
alerts:
  - name: "高错误率"
    condition: "error_rate > 5%"
    duration: "5m"

  - name: "响应时间过长"
    condition: "avg_response_time > 10s"
    duration: "3m"

  - name: "搜索引擎异常"
    condition: "search_engine_error_rate > 10%"
    duration: "2m"
```

## 📞 联系方式

- 项目维护者: GAC AI Team
- 邮箱: <EMAIL>
- 文档: [项目Wiki](wiki-url)
- 问题反馈: [GitHub Issues](issues-url)
