# Online Search Framework 测试套件

## 概述

这个目录包含了 Online Search Framework 的冒烟测试套件，专门用于新闻搜索服务的功能验证和CI/CD流程。测试套件设计用于验证新闻搜索服务的功能完整性、性能表现和错误处理能力。

## 文件说明

### 测试文件

- **`smoke_test.py`** - 统一冒烟测试
  - 服务连通性测试
  - 健康检查测试
  - 新闻搜索接口测试
  - 无关问题识别测试
  - 流式响应测试
  - 参数边界测试
  - 性能测试（5轮）
  - 错误恢复测试

### 支持文件

- **`ci_smoke_test.sh`** - CI环境专用测试脚本
  - 自动检查服务状态
  - 等待服务就绪
  - 运行冒烟测试
  - 生成测试报告

- **`test_utils.py`** - 测试工具函数库
  - JWT令牌管理
  - API客户端封装
  - 测试数据生成
  - 性能分析工具
  - 测试结果收集

## 测试内容详解

### 统一冒烟测试

1. **服务连通性测试** - 验证服务基本连接
2. **健康检查测试** - 测试 `/cabin/web_search/health` 端点
3. **新闻搜索接口测试** - 测试新闻搜索核心功能，使用新闻相关查询
4. **无关问题识别测试** - 验证服务对非新闻相关问题的处理能力
5. **流式响应测试** - 测试stream=true的情况
6. **参数边界测试** - 验证各种参数边界条件（k值、查询长度等）
7. **性能测试（5轮）** - 包含详细的模块耗时分析
   - 首字延迟（TTFT）
   - 关键词提取（keywords）
   - 搜索引擎返回结果（sources）
   - 爬虫结束（fetch）
   - 总耗时（total_time）
8. **错误恢复测试** - 测试服务从错误中恢复的能力

## 使用方法

### 本地运行

#### 方法1: 直接运行测试

```bash
# 进入测试目录
cd online_search_framework/test

# 运行冒烟测试
python3 smoke_test.py

# 指定服务地址
python3 smoke_test.py http://localhost:8080
```

#### 方法2: 使用CI脚本（推荐）

```bash
# 进入测试目录
cd online_search_framework/test

# 运行CI测试脚本
chmod +x ci_smoke_test.sh
./ci_smoke_test.sh

# 或指定服务地址
SERVICE_URL=http://localhost:8080 ./ci_smoke_test.sh
```

### CI/CD集成

测试套件已集成到GitLab CI流程中：

1. **冒烟测试阶段** (`smoke_test`) - 在构建镜像之前运行
2. 只有冒烟测试通过后，才会执行后续的构建和部署阶段
3. 测试失败会阻止整个流水线继续执行

## 环境要求

### Python依赖

- Python 3.7+
- requests库
- PyJWT库

### 系统依赖（CI环境）

- curl（用于健康检查）
- procps（用于进程管理）
- netstat（用于端口检查）

## 配置选项

### 环境变量

- `SERVICE_URL` - 服务地址（默认: http://127.0.0.1:8080）
- `SERVICE_PORT` - 服务端口（默认: 8080）
- `MAX_WAIT_TIME` - 最大等待时间（默认: 120秒）
- `HEALTH_CHECK_INTERVAL` - 健康检查间隔（默认: 5秒）
- `RUN_EXTENDED_TESTS` - 是否运行扩展测试（默认: false）

### JWT认证配置

测试使用预配置的AK/SK：
- AK: `146049b1-0a95-49a2-8856-5e5e35f0f9a6`
- SK: `b8bb9b60-9b3d-4e68-be09-f1e6816794d4`

### 示例

```bash
# 自定义配置运行测试
SERVICE_URL=http://localhost:9000 \
MAX_WAIT_TIME=180 \
RUN_EXTENDED_TESTS=true \
./ci_smoke_test.sh
```

## 输出说明

### 测试结果

- ✅ PASS - 测试通过
- ❌ FAIL - 测试失败
- ⚠️ WARN - 警告（不影响主要功能）
- 每个测试显示耗时和详细信息

### 性能测试报告

包含详细的性能分析：
- 总体响应时间统计（平均、中位数、最大、最小）
- 各模块平均耗时分析
- 成功率统计
- 并发性能表现

### 生成的文件

- 测试日志输出到控制台
- CI环境中的测试结果可通过GitLab CI查看

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用：`netstat -tlnp | grep :8080`
   - 查看服务日志了解详细错误
   - 确认依赖包是否安装完整

2. **JWT认证失败**
   - 检查AK/SK配置是否正确
   - 确认系统时间是否准确
   - 验证PyJWT库是否正确安装

3. **测试超时**
   - 增加MAX_WAIT_TIME环境变量
   - 检查网络连接
   - 确认服务配置正确

4. **依赖缺失**
   - 安装Python依赖: `pip install requests PyJWT`
   - 确认Python版本 >= 3.7

### 调试建议

1. **查看详细日志**
   ```bash
   # 运行单个测试查看详细输出
   python3 smoke_test_001.py
   ```

2. **检查服务状态**
   ```bash
   # 手动检查健康状态
   curl http://localhost:8080/cabin/web_search/health
   ```

3. **验证JWT令牌**
   ```bash
   # 使用test_utils.py验证令牌生成
   python3 test_utils.py
   ```

4. **分步调试**
   - 先运行基础测试（001）
   - 确认基础功能正常后再运行其他测试
   - 使用不同的超时设置

## 扩展

### 添加新测试

1. 创建新的测试文件（如 `smoke_test_005.py`）
2. 继承或使用 `test_utils.py` 中的工具类
3. 实现具体的测试逻辑
4. 更新CI脚本以包含新测试
5. 更新本文档

### 自定义测试配置

可以通过修改测试文件中的配置参数来自定义测试行为：
- 修改测试查询内容
- 调整性能测试轮数
- 更改超时设置
- 添加新的搜索引擎

### 集成其他测试框架

测试套件设计为独立的Python脚本，可以轻松集成到其他测试框架中：
- pytest
- unittest
- nose2

## 版本信息

- **创建时间**: 2025年
- **适用服务**: Online Search Framework
- **CI/CD**: GitLab CI集成
- **测试编号**: 001-004（按功能分类）

## 联系信息

如有问题或建议，请联系开发团队或提交Issue。
