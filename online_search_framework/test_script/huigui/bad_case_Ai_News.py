from bad_case_base_function import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, code_middle_fail, code_complete
from typing_extensions import Optional
from dataclasses import dataclass
import urllib.request
import csv, os, base64, aiohttp, time, asyncio, requests, json, ssl

file_path_in = os.path.dirname(os.path.abspath(__file__)) + "/" + "广汽XAG云端大模型项目(智能体BadCase汇总表（新闻）).csv"
print(file_path_in)

# # excelHelper = ExcelHelper(file_name, sheet_index=0)


file_path_out = os.path.dirname(os.path.abspath(__file__)) + "/" + "Bad_Case_Result_Ai_news_" + str(int(time.time())) + ".csv"

csvHelper = CsvHelper(file_path_in=file_path_in, file_path_out=file_path_out)
judgeHelper = JudgeHelper()


ssl._create_default_https_context = ssl._create_unverified_context
urllib.request.urlopen('https://agent-sit.senseauto.com')


@dataclass
class Result:
    code: int = code_middle_fail
    query: str = ""
    result_text: str = ""


    

class BadCaseRunner:
    @staticmethod
    def check_query_json(query_json_str: str) -> Optional[dict]:
        try:
            return json.loads(query_json_str)
        except BaseException as e:
            print(f"\n[error]: check_query_json: query_json_str: {query_json_str}, e: {str(e)}")
            return None
    
    @staticmethod 
    def build_header() -> dict: 
        return {
            "Authorization": "Bearer app-0lG1fLjvC3qgQTZGEg3sl9gi",
            "Content-Type": "application/json"
        }

    
    @staticmethod
    async def run_single_case(query: dict, more_round: Optional[list]) -> Result :
        result = Result()
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        conversation_id = None
        async with aiohttp.ClientSession() as session:
            try:
                streamHelper = AgentStreamHelper()    
                async with session.post(
                        "https://agent-sit.senseauto.com/v1/chat-messages",
                        read_bufsize=4194304,
                        headers=BadCaseRunner.build_header(),
                        json=query, ssl=False) as response:
                    response.raise_for_status()
                    
                    async for raw_chunk in response.content:
                        chunk = raw_chunk.decode('utf-8').strip()
                        
                        if not chunk:
                            continue

                        if chunk.startswith("data: "):
                            json_str = chunk[len("data: "):]
                            streamHelper.parse_stream_line(json_str)
                
                result.code = code_complete
                result.query = query.get("query")
                conversation_id = streamHelper.get_conversation_id()
                result_text = streamHelper.get_total_text_result()
                result.result_text = result_text
                
            except BaseException as e:
                print(f"\n[error]: run_single_case: query: {query}, e: {str(e)}")
            
            if more_round:
                for more_round_item in more_round:
                    if isinstance(more_round_item, str):
                        async with aiohttp.ClientSession() as session:
                            try:
                                query["query"] = more_round_item
                                streamHelper = AgentStreamHelper()    
                                if conversation_id:
                                    query["conversation_id"] = conversation_id
                                async with session.post(
                                        "https://agent-sit.senseauto.com/v1/chat-messages",
                                        read_bufsize=4194304,
                                        headers=BadCaseRunner.build_header(),
                                        json=query, ssl=False) as response:
                                    response.raise_for_status()
                                    
                                    async for raw_chunk in response.content:
                                        chunk = raw_chunk.decode('utf-8').strip()
                                        
                                        if not chunk:
                                            continue

                                        if chunk.startswith("data: "):
                                            json_str = chunk[len("data: "):]
                                            streamHelper.parse_stream_line(json_str)
                                
                                result.code = code_complete
                                result.query = query.get("query")
                                conversation_id = streamHelper.get_conversation_id()
                                result_text = streamHelper.get_total_text_result()
                                result.result_text = result_text
                                
                            except BaseException as e:
                                print(f"\n[error]: run_single_case: query: {query}, e: {str(e)}")
        return result
    
    async def run_bad_cases(self, ):
        async for index, row in csvHelper.read_line():
            query_json_str = row[0]
            more_round = row[1]
            old_answer = row[2]
            requirement = row[3]
            query_json = BadCaseRunner.check_query_json(query_json_str)
            more_round_list = BadCaseRunner.check_more_round_list(more_round)
            if not query_json:
                row.append("原始问题输入有误，请重新确认是否是有效内容")
            else:
                print(f"[process].  run_single_case. index: {index}, more_round: {more_round_list}")
                single_case_result = await BadCaseRunner.run_single_case(query_json, more_round_list)
                if single_case_result.code == code_complete:
                    judge_result = await judgeHelper.judge_result(query=query_json_str, answer=single_case_result.result_text, requirement= requirement)
                    row.append(judge_result)
                    row.append(single_case_result.result_text)
                else:
                    row.append("Agent处理异常，请重新确认是否是有效内容")
            await csvHelper.write_back_to_line(row)


badCaseRunner = BadCaseRunner()



if __name__ == "__main__":
    loop = asyncio.new_event_loop()

    try:
        asyncio.set_event_loop(loop=loop)
        loop.run_until_complete(badCaseRunner.run_bad_cases())
    finally:
        loop.close()