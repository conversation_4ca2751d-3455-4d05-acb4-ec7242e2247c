stages:
  - smoke_test
  - extract_version
  - build_image
#  - deploy_diamond_prd
  - deploy_sit
  - deploy_prd

variables:
  K8S_SERVICE_NAME:
    value: "gac-online-search-service"
    description: "The service name of K8S"
  ECR_REPO_NAME:
    value: "cabin-online-search-service"
    description: "The repository name of Elastic Container Repository, will create one if not exist"
  IMAGE_TAG_OVERWRITE:
    value: ""
    description: "Overwrite docker image version defined in this project"

smoke_test_job:
  stage: smoke_test
  image: python:3.11-slim
  variables:
    PYTHONPATH: "/builds/$CI_PROJECT_PATH"
    SERVICE_URL: "http://127.0.0.1:8080"
    SERVICE_PORT: "8080"
  before_script:
    - apt-get update && apt-get install -y curl procps git net-tools
    - echo "🔍 检查项目文件..."
    - ls -la
    - echo "📦 安装Python依赖..."
    - pip install --upgrade pip
    - pip install typing_extensions  # 确保兼容性
    - pip install requests PyJWT
    - pip install -r requirements.txt
    - echo "🚀 在后台启动服务..."
    - cat test/.env
    - source test/.env
    - echo "TENCENT_SK:"$TENCENT_SK
    - echo "TENCENT_AK:"$TENCENT_AK
    - nohup python3 main.py --port 8080
    - echo "⏳ 等待服务启动..."
    - sleep 30
    - echo "📋 检查服务状态:"
    - ps aux | grep python | head -5
    - netstat -tlnp | grep :8080 || echo "端口8080未监听"
    - echo "📋 服务启动日志（最后30行）:"
    - tail -30 service.log || echo "No service log found"
  script:
    - cd test
    - echo ""
    - echo "🧪 开始运行冒烟测试..."
    - chmod +x ci_smoke_test.sh
    - ./ci_smoke_test.sh > smoke_test_results.log 2>&1
  after_script:
    - echo "📋 最终服务日志:"
    - tail -50 service.log || echo "No service log found"
    - echo "📋 进程状态:"
    - ps aux | grep python || echo "No python processes"
    - cd test
    - sleep 5 && cat smoke_test_results.log
  tags:
    - docker
  artifacts:
    when: always
    paths:
      - test/smoke_test_results.log
      - service.log
    expire_in: 1 week
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
    - if: '$CI_PIPELINE_SOURCE == "push"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  allow_failure: false

extract_version_job:
  needs:
    - smoke_test_job
  stage: extract_version
  image:
    name: alpine/git
#    entrypoint: [""]
  script:
    - GIT_TAG=`git describe --tags --abbrev=0 --always | sed 's/^V//' | sed 's/^v//'`
    - echo $GIT_TAG
    - echo "IMAGE_VERSION=$GIT_TAG" >> build.env
    - echo "PROJECT_VERSION=$GIT_TAG" >> build.env
  tags: # tags 参数，用于选择执行 job 的 runner
    - k8s                                  # job 会在标记为 k8s 的 runner 上运行
  artifacts:
    reports:
      dotenv: build.env
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG'
      when: always

build_and_push_image_job:
  stage: build_image
  image:                                   # 设置运行此 job 使用的 docker image
    name: registry.sensetime.com/senseautocameraservice/cicd-docker-image:1.10-aws    # 用于执行这个 job 用到的 docker image，即 script 中的命令是在基于这个 docker image 启动的 container 中执行的
    entrypoint: [ "" ]
  tags: # tags 参数，用于选择执行 job 的 runner
    - docker                                  # job 会在标记为 docker 的 runner 上运行dind 功能
  script:
    - if [ -z ${IMAGE_TAG_OVERWRITE} ]; then CUSTOM_TAG=${PROJECT_VERSION}; fi
    - if [ ! -z ${IMAGE_TAG_OVERWRITE} ]; then CUSTOM_TAG=${IMAGE_TAG_OVERWRITE}; fi
    - echo $CUSTOM_TAG
    - echo "CUSTOM_TAG=$CUSTOM_TAG" >> build.env
    - echo "PROJECT_VERSION=$PROJECT_VERSION" >> build.env
    ## push to harbor
    - docker login registry.sensetime.com -u $username -p $password
    - docker build -t registry.sensetime.com/senseautocameraservice/$ECR_REPO_NAME:$CUSTOM_TAG .
    - docker push registry.sensetime.com/senseautocameraservice/$ECR_REPO_NAME:$CUSTOM_TAG

    - aws ecr get-login-password --region cn-northwest-1 | docker login --username AWS --password-stdin 613018107236.dkr.ecr.cn-northwest-1.amazonaws.com.cn
    - aws ecr describe-repositories --repository-names $ECR_REPO_NAME --region cn-northwest-1 || aws ecr create-repository --repository-name $ECR_REPO_NAME --region cn-northwest-1 # 如果不存在则创建repository
    - docker build -t 613018107236.dkr.ecr.cn-northwest-1.amazonaws.com.cn/$ECR_REPO_NAME:$CUSTOM_TAG .
    - docker push 613018107236.dkr.ecr.cn-northwest-1.amazonaws.com.cn/$ECR_REPO_NAME:$CUSTOM_TAG

    - aws ecr get-login-password --region cn-north-1 | docker login --username AWS --password-stdin 613018107236.dkr.ecr.cn-north-1.amazonaws.com.cn
    - aws ecr describe-repositories --repository-names $ECR_REPO_NAME --region cn-north-1 || aws ecr create-repository --repository-name $ECR_REPO_NAME --region cn-north-1 # 如果不存在则创建repository
    - docker build -t 613018107236.dkr.ecr.cn-north-1.amazonaws.com.cn/$ECR_REPO_NAME:$CUSTOM_TAG .
    - docker push 613018107236.dkr.ecr.cn-north-1.amazonaws.com.cn/$ECR_REPO_NAME:$CUSTOM_TAG
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG'
      when: always
  artifacts:
    reports:
      dotenv: build.env


#deploy_diamond_prd:
#  needs:
#    - build_and_push_image_job
#  stage: deploy_diamond_prd
#  image: registry.sensetime.com/senseautocameraservice/cicd-docker-image:1.10-aws
#  script:
#    - echo "Deploy $K8S_SERVICE_NAME service to prd cluster"
#    - kubectx prd
#    - kubectx -c
#    - echo "CUSTOM_TAG=$CUSTOM_TAG"
#    - helm upgrade --install $K8S_SERVICE_NAME ./pipeline -f pipeline/values/prd/values.yaml --reset-values --set application.version=${CUSTOM_TAG} --set image.tag=${CUSTOM_TAG}
#    - echo "CUSTOM_TAG=$CUSTOM_TAG" >> build.env
#  rules:
#    - if: '$CI_PIPELINE_SOURCE == "web"'
#      when: manual
#      allow_failure: false
#    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG'
#      when: manual
#      allow_failure: false
#  artifacts:
#    reports:
#      dotenv: build.env


deploy_k8s_sit:
  needs:
    - build_and_push_image_job
  stage: deploy_sit
  image: registry.sensetime.com/senseautocameraservice/cicd-docker-image:1.10-aws
  script:
    - echo "Deploy $K8S_SERVICE_NAME service to auto-cloud-sit cluster"
    - kubectx auto-cloud-sit
    - kubectx -c
    - echo "CUSTOM_TAG=$CUSTOM_TAG"
    - helm upgrade --install $K8S_SERVICE_NAME ./pipeline -f pipeline/values/auto-cloud-sit/values.yaml --reset-values --set application.version=${CUSTOM_TAG} --set image.tag=${CUSTOM_TAG}
    - echo "CUSTOM_TAG=$CUSTOM_TAG" >> build.env
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
      when: manual
      allow_failure: false
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG'
      when: manual
      allow_failure: false
  artifacts:
    reports:
      dotenv: build.env

deploy_k8s_prd:
  needs:
    - deploy_k8s_sit
  stage: deploy_prd
  image: registry.sensetime.com/senseautocameraservice/cicd-docker-image:1.10-aws
  script:
    - echo "Deploy $K8S_SERVICE_NAME service to auto-cloud-prd cluster"
    - kubectx auto-cloud-prd
    - kubectx -c
    - echo "CUSTOM_TAG=$CUSTOM_TAG"
    - helm upgrade --install $K8S_SERVICE_NAME ./pipeline -f pipeline/values/auto-cloud-prd/values.yaml --reset-values --set application.version=${CUSTOM_TAG} --set image.tag=${CUSTOM_TAG}
    - echo "CUSTOM_TAG=$CUSTOM_TAG" >> build.env
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
      when: manual
      allow_failure: false
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_TAG'
      when: manual
      allow_failure: false
  artifacts:
    reports:
      dotenv: build.env