import pandas as pd
from openpyxl import load_workbook
from typing_extensions import Optional
from openai import OpenAI
import csv, json


code_middle_fail = 100
code_complete = 0


class ExcelHelper:
    def __init__(self, file_path, sheet_index: int = -1):
        self.file_path=file_path
        self.workbook=load_workbook(file_path)
        if 0 <= sheet_index:
            self.sheet=self.workbook._sheets[sheet_index]
        else:
            self.sheet=self.workbook.active



    async def read_each_line(self):
        for row in self.sheet.iter_rows():
            yield row
        
    
class CsvHelper:
    def __init__(self, file_path_in, file_path_out):
        self.file_path_in = file_path_in
        self.file_path_out = file_path_out
        self.index=0
        self.file_in = open(self.file_path_in, mode='r', newline='', encoding='utf-8')
        self.file_out = open(self.file_path_out, mode='w', newline='', encoding='utf-8-sig')
        self.reader = csv.reader(self.file_in)
        self.writer = csv.writer(self.file_out)


    def close_file(self):
        try:
            self.file_in.close()
        except BaseException as e:
            print(e)
        try:
            self.file_out.close()
        except BaseException as e:
            print(e)

    async def read_line(self, start_index = 1):
        for row in self.reader:
            self.index = self.index + 1
            if self.index <= start_index:
                await self.write_back_to_line(row)
                continue
            yield self.index - 1, row
  
            
    async def write_back_to_line(self, content: list):
        self.writer.writerow(content)

            
            

class AgentStreamHelper:
    def __init__(self):
        self.total_message = ""
        self.json_data = []
        self.log_data = []
        self.conversation_id = None

    def _parse_message(self, data: dict):
        answer = data.get("answer", "")
        answer_json = json.loads(answer)
        answer_type = answer_json.get("type")
        if "text" == answer_type:
            self.total_message = self.total_message + answer_json.get("data", "")
        if "json" == answer_type:
            self.json_data.append(answer_json)

    def _parse_agent_log(self, data: dict):
        label = data.get("data", {}).get("label", "")
        content = data.get("data", {}).get("data", {})
        if isinstance(content, dict):
            content["original_label"] = label
            self.log_data.append(content)

    def _parse_workflow_started(self, data: dict):
        self.conversation_id = data.get("conversation_id")

    def parse_stream_line(self, line: str):
        try:
            json_item = json.loads(line)
            event = json_item.get("event")
            if "agent_log" == event:
                self._parse_agent_log(json_item)
            if "message" == event:
                self._parse_message(json_item)
            if "workflow_started" == event:
                self._parse_workflow_started(json_item)
               
        except BaseException as e:
            print(f"\n[error] parse_stream_line:  e: {str(e)}. \nline: {line}")

    def get_conversation_id(self, ) -> Optional[str]:
        return self.conversation_id

    def get_total_text_result(self, ):
        return self.total_message

    def get_agent_log(self, filter: Optional[str] = None) -> list:
        if not filter:
            return self.log_data
        else:
            result = []
            for item in self.json_data:
                if filter == item.get("original_label"):
                    result.append(item)
            return result
    
    def get_json_data(self, filter: Optional[str] = None) -> list:
        if not filter:
            return self.json_data
        else:
            result = []
            for item in self.json_data:
                if filter == item.get("region"):
                    result.append(item)
            return result


class JudgeHelper: 
    def __init__(self):
        self.apikey = "1s3963nw8802M4O55yMuU6x37tOYQ682"
        self.url = "http://58.22.103.26:8099/v1"
        self.modelname = "SenseAuto-Chat"
        self.client = OpenAI(api_key=self.apikey, base_url=self.url)

    async def judge_result(self, query: str, answer: str, requirement: str) -> Optional[str]:
        prompt_base = """
        你是一个问答结果质量判断专家，根据输入的问题、生成的结果, 以及对问答的要求，综合判断生成的结果是否满足要求。
        要求：
            -你的回答要在开头就表明判断的定论，用：满足要求、基本满足要求、无法判断、不满足要求，来给出一个明确的判断
            -之后要简要的说明你的判断依据
        输入：
            问题:{query}
            生成的结果:{answer}
            对问答的要求:{requirement}
        """
        prompt = prompt_base.format(query=query, answer=answer, requirement = requirement)

        massage: list = [
            {"role":"system", "content": prompt},
            {"role":"user", "content":"请作出回应"}
        ]

        response = self.client.chat.completions.create(
                model=self.modelname,
                messages=massage,
                stream=False
        )


        return response.choices[0].message.content